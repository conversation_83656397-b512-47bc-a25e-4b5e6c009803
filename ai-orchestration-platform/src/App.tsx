import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { Bot, Settings, Home, Activity } from 'lucide-react';

// Main Dashboard Component
const Dashboard: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">AI Orchestration Platform</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md border">
          <div className="flex items-center mb-4">
            <Bot className="h-8 w-8 text-blue-600 mr-3" />
            <h2 className="text-xl font-semibold">AI Agents</h2>
          </div>
          <p className="text-gray-600">Manage and orchestrate your AI agents</p>
          <div className="mt-4">
            <span className="inline-block bg-green-100 text-green-800 text-sm px-2 py-1 rounded">
              3 Active
            </span>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md border">
          <div className="flex items-center mb-4">
            <Activity className="h-8 w-8 text-green-600 mr-3" />
            <h2 className="text-xl font-semibold">Workflows</h2>
          </div>
          <p className="text-gray-600">Design and monitor AI workflows</p>
          <div className="mt-4">
            <span className="inline-block bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded">
              5 Running
            </span>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md border">
          <div className="flex items-center mb-4">
            <Settings className="h-8 w-8 text-purple-600 mr-3" />
            <h2 className="text-xl font-semibold">Configuration</h2>
          </div>
          <p className="text-gray-600">Configure system settings and integrations</p>
          <div className="mt-4">
            <span className="inline-block bg-gray-100 text-gray-800 text-sm px-2 py-1 rounded">
              Ready
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Agents Page Component
const Agents: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">AI Agents</h1>
      <div className="bg-white rounded-lg shadow-md border p-6">
        <p className="text-gray-600">AI agent management interface coming soon...</p>
      </div>
    </div>
  );
};

// Workflows Page Component
const Workflows: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Workflows</h1>
      <div className="bg-white rounded-lg shadow-md border p-6">
        <p className="text-gray-600">Workflow designer and monitor coming soon...</p>
      </div>
    </div>
  );
};

// Settings Page Component
const SettingsPage: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Settings</h1>
      <div className="bg-white rounded-lg shadow-md border p-6">
        <p className="text-gray-600">System configuration panel coming soon...</p>
      </div>
    </div>
  );
};

// Navigation Component
const Navigation: React.FC = () => {
  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Bot className="h-8 w-8 text-blue-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">AI Platform</span>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              <Link
                to="/"
                className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
              >
                <Home className="inline h-4 w-4 mr-1" />
                Dashboard
              </Link>
              <Link
                to="/agents"
                className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
              >
                <Bot className="inline h-4 w-4 mr-1" />
                Agents
              </Link>
              <Link
                to="/workflows"
                className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
              >
                <Activity className="inline h-4 w-4 mr-1" />
                Workflows
              </Link>
              <Link
                to="/settings"
                className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
              >
                <Settings className="inline h-4 w-4 mr-1" />
                Settings
              </Link>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

// Main App Component
const App: React.FC = () => {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <main>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/agents" element={<Agents />} />
            <Route path="/workflows" element={<Workflows />} />
            <Route path="/settings" element={<SettingsPage />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
};

export default App;
